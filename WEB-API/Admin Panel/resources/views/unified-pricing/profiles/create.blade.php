@extends('layouts.app')

@section('title', trans('unified_pricing.profiles.create_new'))

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3 mb-md-4">
        <div class="col-12">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <div class="mb-3 mb-md-0">
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-plus-circle text-primary"></i>
                        {{trans('unified_pricing.profiles.create_new')}}
                    </h1>
                    <p class="text-muted mb-0 small">{{trans('unified_pricing.profiles.create_description')}}</p>
                </div>
                <div class="btn-group flex-column flex-sm-row" role="group">
                    <a href="{{ route('unified-pricing.profiles') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> {{trans('unified_pricing.profiles.back_to_profiles')}}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Creation Form -->
    <div class="row">
        <div class="col-xl-8 col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{trans('unified_pricing.profiles.profile_information')}}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('unified-pricing.profiles.store') }}" method="POST">
                        @csrf

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="name">{{trans('unified_pricing.profiles.profile_name')}} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <small class="form-text text-muted">{{trans('unified_pricing.profiles.internal_name_help')}}</small>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="display_name">{{trans('unified_pricing.profiles.display_name')}} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="display_name" name="display_name" required>
                                    <small class="form-text text-muted">{{trans('unified_pricing.profiles.display_name_help')}}</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">{{trans('unified_pricing.profiles.description')}}</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="{{trans('unified_pricing.profiles.description_placeholder')}}"></textarea>
                        </div>

                        <!-- Profile Type -->
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="profile_type">{{trans('unified_pricing.profiles.profile_type')}} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="profile_type" name="profile_type" required>
                                        <option value="">{{trans('unified_pricing.profiles.select_type')}}</option>
                                        <option value="starter">{{trans('unified_pricing.profile_types.starter')}}</option>
                                        <option value="standard">{{trans('unified_pricing.profile_types.standard')}}</option>
                                        <option value="premium">{{trans('unified_pricing.profile_types.premium')}}</option>
                                        <option value="enterprise">{{trans('unified_pricing.profile_types.enterprise')}}</option>
                                        <option value="promotional">{{trans('unified_pricing.profile_types.promotional')}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="business_model">{{trans('unified_pricing.profiles.business_model')}} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="business_model" name="business_model" required>
                                        <option value="">{{trans('unified_pricing.profiles.select_model')}}</option>
                                        <option value="percentage">{{trans('unified_pricing.business_models.percentage')}}</option>
                                        <option value="fixed">{{trans('unified_pricing.business_models.fixed')}}</option>
                                        <option value="hybrid">{{trans('unified_pricing.business_models.hybrid')}}</option>
                                        <option value="tiered">{{trans('unified_pricing.business_models.tiered')}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Commission Rates -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-info">{{trans('unified_pricing.profiles.commission_rates')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="admin_commission_rate">{{trans('unified_pricing.profiles.admin_commission_rate')}}</label>
                                            <input type="number" class="form-control" id="admin_commission_rate"
                                                   name="admin_commission_rate" step="0.01" min="0" max="100">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="driver_commission_rate">{{trans('unified_pricing.profiles.driver_commission_rate')}}</label>
                                            <input type="number" class="form-control" id="driver_commission_rate"
                                                   name="driver_commission_rate" step="0.01" min="0" max="100">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-12 col-sm-12">
                                        <div class="form-group">
                                            <label for="platform_fee_rate">{{trans('unified_pricing.profiles.platform_fee_rate')}}</label>
                                            <input type="number" class="form-control" id="platform_fee_rate"
                                                   name="platform_fee_rate" step="0.01" min="0" max="100">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="admin_fixed_fee">{{trans('unified_pricing.profiles.admin_fixed_fee')}}</label>
                                            <input type="number" class="form-control" id="admin_fixed_fee"
                                                   name="admin_fixed_fee" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="driver_fixed_fee">{{trans('unified_pricing.profiles.driver_fixed_fee')}}</label>
                                            <input type="number" class="form-control" id="driver_fixed_fee"
                                                   name="driver_fixed_fee" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="minimum_order_value">Minimum Order Value ($)</label>
                                    <input type="number" class="form-control" id="minimum_order_value" 
                                           name="minimum_order_value" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="maximum_commission_amount">Maximum Commission Amount ($)</label>
                                    <input type="number" class="form-control" id="maximum_commission_amount" 
                                           name="maximum_commission_amount" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- Status and Options -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" checked>
                                        <label class="custom-control-label" for="is_active">Active Profile</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_default" name="is_default">
                                        <label class="custom-control-label" for="is_default">Default Profile</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="auto_assignable" name="auto_assignable" checked>
                                <label class="custom-control-label" for="auto_assignable">Auto-Assignable to New Restaurants</label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Profile
                            </button>
                            <a href="{{ route('unified-pricing.profiles') }}" class="btn btn-secondary ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Profile Types Guide</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Starter</h6>
                        <p class="small text-muted">For new restaurants with reduced rates to encourage adoption.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-secondary">Standard</h6>
                        <p class="small text-muted">Default rates for established restaurants.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-info">Premium</h6>
                        <p class="small text-muted">Reduced rates for high-volume restaurants.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-success">Enterprise</h6>
                        <p class="small text-muted">Custom rates for restaurant chains and large partners.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-warning">Promotional</h6>
                        <p class="small text-muted">Special campaign rates for limited time offers.</p>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Business Models</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Percentage</h6>
                        <p class="small text-muted">Commission based on order value percentage.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-secondary">Fixed</h6>
                        <p class="small text-muted">Fixed amount per order regardless of value.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-info">Hybrid</h6>
                        <p class="small text-muted">Combination of percentage and fixed amount.</p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-success">Tiered</h6>
                        <p class="small text-muted">Different rates based on order value ranges.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
