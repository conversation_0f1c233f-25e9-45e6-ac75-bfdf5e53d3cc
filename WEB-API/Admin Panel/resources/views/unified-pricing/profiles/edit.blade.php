@extends('layouts.app')

@section('title', trans('unified_pricing.profiles.edit_profile'))

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3 mb-md-4">
        <div class="col-12">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <div class="mb-3 mb-md-0">
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary"></i>
                        {{trans('unified_pricing.profiles.edit_profile')}}
                    </h1>
                    <p class="text-muted mb-0 small">{{trans('unified_pricing.profiles.profile_details')}}</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('unified-pricing.profiles')}}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i>
                        <span class="d-none d-sm-inline">{{trans('unified_pricing.profiles.back_to_profiles')}}</span>
                        <span class="d-sm-none">{{trans('lang.back')}}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> {{ session('success')}}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> {{ session('error')}}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> <strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <!-- Profile Edit Form -->
    <div class="row">
        <div class="col-xl-8 col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{trans('unified_pricing.profiles.profile_information')}}</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('unified-pricing.profiles.update', $id)}}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="name">{{trans('unified_pricing.profiles.profile_name')}} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $profile->name ?? '')}}" required>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="display_name">{{trans('unified_pricing.profiles.display_name')}} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="display_name" name="display_name" value="{{ old('display_name', $profile->display_name ?? '')}}" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">{{trans('unified_pricing.profiles.description')}}</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ old('description', $profile->description ?? '')}}</textarea>
                        </div>

                        <!-- Profile Type -->
                        <div class="row">
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="profile_type">{{trans('unified_pricing.profiles.profile_type')}} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="profile_type" name="profile_type" required>
                                        <option value="starter" {{ old('profile_type', $profile->profile_type ?? '') == 'starter' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.starter')}}</option>
                                        <option value="standard" {{ old('profile_type', $profile->profile_type ?? '') == 'standard' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.standard')}}</option>
                                        <option value="premium" {{ old('profile_type', $profile->profile_type ?? '') == 'premium' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.premium')}}</option>
                                        <option value="enterprise" {{ old('profile_type', $profile->profile_type ?? '') == 'enterprise' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.enterprise')}}</option>
                                        <option value="promotional" {{ old('profile_type', $profile->profile_type ?? '') == 'promotional' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.promotional')}}</option>
                                        <option value="custom" {{ old('profile_type', $profile->profile_type ?? '') == 'custom' ? 'selected' : '' }}>{{trans('unified_pricing.profile_types.custom')}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <div class="form-group">
                                    <label for="business_model">{{trans('unified_pricing.profiles.business_model')}} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="business_model" name="business_model" required>
                                        <option value="percentage" {{ old('business_model', $profile->business_model ?? '') == 'percentage' ? 'selected' : '' }}>{{trans('unified_pricing.business_models.percentage')}}</option>
                                        <option value="fixed" {{ old('business_model', $profile->business_model ?? '') == 'fixed' ? 'selected' : '' }}>{{trans('unified_pricing.business_models.fixed')}}</option>
                                        <option value="hybrid" {{ old('business_model', $profile->business_model ?? '') == 'hybrid' ? 'selected' : '' }}>{{trans('unified_pricing.business_models.hybrid')}}</option>
                                        <option value="tiered" {{ old('business_model', $profile->business_model ?? '') == 'tiered' ? 'selected' : '' }}>{{trans('unified_pricing.business_models.tiered')}}</option>
                                        <option value="performance" {{ old('business_model', $profile->business_model ?? '') == 'performance' ? 'selected' : '' }}>{{trans('unified_pricing.business_models.performance')}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Commission Rates -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-info">{{trans('unified_pricing.profiles.commission_rates')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="admin_commission_rate">{{trans('unified_pricing.profiles.admin_commission_rate')}}</label>
                                            <input type="number" class="form-control" id="admin_commission_rate"
                                                   name="admin_commission_rate" step="0.01" min="0" max="100"
                                                   value="{{ old('admin_commission_rate', $profile->admin_commission_rate ?? '12.00')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="driver_commission_rate">{{trans('unified_pricing.profiles.driver_commission_rate')}}</label>
                                            <input type="number" class="form-control" id="driver_commission_rate"
                                                   name="driver_commission_rate" step="0.01" min="0" max="100"
                                                   value="{{ old('driver_commission_rate', $profile->driver_commission_rate ?? '8.00')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-12 col-sm-12">
                                        <div class="form-group">
                                            <label for="platform_fee_rate">{{trans('unified_pricing.profiles.platform_fee_rate')}}</label>
                                            <input type="number" class="form-control" id="platform_fee_rate"
                                                   name="platform_fee_rate" step="0.01" min="0" max="100"
                                                   value="{{ old('platform_fee_rate', $profile->platform_fee_rate ?? '2.00')}}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="fixed_fee_per_order">{{trans('unified_pricing.profiles.fixed_fee_per_order')}}</label>
                                            <input type="number" class="form-control" id="fixed_fee_per_order"
                                                   name="fixed_fee_per_order" step="0.01" min="0"
                                                   value="{{ old('fixed_fee_per_order', $profile->fixed_fee_per_order ?? '0.00')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="payment_processing_fee">{{trans('unified_pricing.profiles.payment_processing_fee')}}</label>
                                            <input type="number" class="form-control" id="payment_processing_fee"
                                                   name="payment_processing_fee" step="0.01" min="0"
                                                   value="{{ old('payment_processing_fee', $profile->payment_processing_fee ?? '0.00')}}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Delivery Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-success">{{trans('unified_pricing.profiles.delivery_settings')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="base_delivery_charge">{{trans('unified_pricing.profiles.base_delivery_charge')}}</label>
                                            <input type="number" class="form-control" id="base_delivery_charge"
                                                   name="base_delivery_charge" step="0.01" min="0"
                                                   value="{{ old('base_delivery_charge', $profile->base_delivery_charge ?? '3.00')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="per_km_delivery_rate">{{trans('unified_pricing.profiles.per_km_delivery_rate')}}</label>
                                            <input type="number" class="form-control" id="per_km_delivery_rate"
                                                   name="per_km_delivery_rate" step="0.01" min="0"
                                                   value="{{ old('per_km_delivery_rate', $profile->per_km_delivery_rate ?? '1.50')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-12 col-sm-12">
                                        <div class="form-group">
                                            <label for="free_delivery_threshold">{{trans('unified_pricing.profiles.free_delivery_threshold')}}</label>
                                            <input type="number" class="form-control" id="free_delivery_threshold"
                                                   name="free_delivery_threshold" step="0.01" min="0"
                                                   value="{{ old('free_delivery_threshold', $profile->free_delivery_threshold ?? '25.00')}}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="min_delivery_charge">{{trans('unified_pricing.profiles.min_delivery_charge')}}</label>
                                            <input type="number" class="form-control" id="min_delivery_charge"
                                                   name="min_delivery_charge" step="0.01" min="0"
                                                   value="{{ old('min_delivery_charge', $profile->min_delivery_charge ?? '2.00')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="max_delivery_charge">{{trans('unified_pricing.profiles.max_delivery_charge')}}</label>
                                            <input type="number" class="form-control" id="max_delivery_charge"
                                                   name="max_delivery_charge" step="0.01" min="0"
                                                   value="{{ old('max_delivery_charge', $profile->max_delivery_charge ?? '15.00')}}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Eligibility Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-warning">{{trans('unified_pricing.profiles.eligibility_requirements')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="min_monthly_orders">{{trans('unified_pricing.profiles.min_monthly_orders')}}</label>
                                            <input type="number" class="form-control" id="min_monthly_orders"
                                                   name="min_monthly_orders" min="0"
                                                   value="{{ old('min_monthly_orders', $profile->min_monthly_orders ?? '0')}}">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label for="min_rating_required">{{trans('unified_pricing.profiles.min_rating_required')}}</label>
                                            <input type="number" class="form-control" id="min_rating_required"
                                                   name="min_rating_required" step="0.1" min="0" max="5"
                                                   value="{{ old('min_rating_required', $profile->min_rating_required ?? '0.0')}}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-secondary">{{trans('unified_pricing.profiles.profile_settings')}}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1"
                                                       {{ old('is_active', $profile->is_active ?? true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_active">{{trans('unified_pricing.profiles.is_active')}}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="auto_assign_eligible" name="auto_assign_eligible" value="1"
                                                       {{ old('auto_assign_eligible', $profile->auto_assign_eligible ?? false) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="auto_assign_eligible">{{trans('unified_pricing.profiles.auto_assign_eligible')}}</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-12 col-sm-12">
                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="requires_approval" name="requires_approval" value="1"
                                                       {{ old('requires_approval', $profile->requires_approval ?? true) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="requires_approval">{{trans('unified_pricing.profiles.requires_approval')}}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group">
                            <div class="d-flex flex-column flex-sm-row">
                                <button type="submit" class="btn btn-primary mb-2 mb-sm-0 mr-sm-2" id="updateProfileBtn">
                                    <i class="fas fa-save"></i> {{trans('unified_pricing.profiles.update_profile')}}
                                </button>
                                <a href="{{ route('unified-pricing.profiles')}}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> {{trans('unified_pricing.profiles.cancel')}}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Statistics -->
        <div class="col-xl-4 col-lg-12 mt-4 mt-xl-0">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">{{trans('unified_pricing.profiles.profile_statistics')}}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6 col-xl-12">
                            <div class="mb-3">
                                <h6 class="text-primary">{{trans('unified_pricing.profiles.active_restaurants')}}</h6>
                                <h4 class="text-gray-800">{{ $profile->active_restaurants_count ?? 0 }}</h4>
                                <p class="small text-muted">{{trans('unified_pricing.profiles.restaurants_using_profile')}}</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="text-success">{{trans('unified_pricing.profiles.total_orders')}}</h6>
                                <h4 class="text-gray-800">{{ $profile->total_orders_count ?? 0 }}</h4>
                                <p class="small text-muted">{{trans('unified_pricing.profiles.orders_processed')}}</p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-xl-12">
                            <div class="mb-3">
                                <h6 class="text-info">{{trans('unified_pricing.profiles.commission_earned')}}</h6>
                                <h4 class="text-gray-800">${{ number_format($profile->total_commission ?? 0, 2)}}</h4>
                                <p class="small text-muted">{{trans('unified_pricing.profiles.commission_from_profile')}}</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="text-warning">{{trans('unified_pricing.profiles.average_order_value')}}</h6>
                                <h4 class="text-gray-800">${{ number_format($profile->avg_order_value ?? 0, 2)}}</h4>
                                <p class="small text-muted">{{trans('unified_pricing.profiles.avg_order_for_profile')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow border-danger mt-4">
                <div class="card-header bg-danger text-white py-3">
                    <h6 class="m-0 font-weight-bold text-white">{{trans('unified_pricing.profiles.danger_zone')}}</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">{{trans('unified_pricing.profiles.delete_warning')}}</p>
                    <form action="{{ route('unified-pricing.profiles.destroy', $id)}}" method="POST"
                          onsubmit="return confirm('{{trans('unified_pricing.profiles.delete_confirm')}}')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> {{trans('unified_pricing.profiles.delete_profile')}}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* RTL Support */
[dir="rtl"] .form-group label {
    text-align: right;
}

[dir="rtl"] .btn-group {
    flex-direction: row-reverse;
}

[dir="rtl"] .custom-control-label {
    padding-right: 1.5rem;
    padding-left: 0;
}

[dir="rtl"] .custom-control-label::before,
[dir="rtl"] .custom-control-label::after {
    right: -1.5rem;
    left: auto;
}

/* Enhanced Responsive Design */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-body {
        padding: 1rem 0.75rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .form-control {
        font-size: 0.875rem;
    }

    .h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .btn-group {
        margin-top: 1rem;
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
    }
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Custom switch improvements for mobile */
@media (max-width: 576px) {
    .custom-control-label {
        font-size: 0.875rem;
        line-height: 1.4;
    }
}
</style>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Profile edit page loaded');

    // Add form submission debugging
    const form = document.querySelector('form');
    const updateBtn = document.getElementById('updateProfileBtn');

    if (form && updateBtn) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission started');

            // Disable button to prevent double submission
            updateBtn.disabled = true;
            updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {{ __("unified_pricing.profiles.updating")}}';

            // Log form data for debugging
            const formData = new FormData(form);
            console.log('Form data being submitted:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }
        });
    }

    // Add change detection for debugging
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            console.log(`Field changed: ${this.name} = ${this.value}`);
        });
    });

    // Enhanced responsive behavior
    function handleResponsiveLayout() {
        const isMobile = window.innerWidth < 768;
        const cards = document.querySelectorAll('.card');

        cards.forEach(card => {
            if (isMobile) {
                card.classList.add('mb-3');
                card.classList.remove('mb-4');
            } else {
                card.classList.add('mb-4');
                card.classList.remove('mb-3');
            }
        });
    }

    // Initial layout adjustment
    handleResponsiveLayout();

    // Handle window resize
    window.addEventListener('resize', handleResponsiveLayout);

    console.log('Profile edit debugging and responsive features initialized');
});
</script>
@endsection
