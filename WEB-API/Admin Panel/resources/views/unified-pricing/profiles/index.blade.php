@extends('layouts.app')

@section('content')
<div class="page-wrapper">
    <!-- Page Titles - Exact Match to Zone Page -->
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">{{trans('unified_pricing.profiles.page_title') ?: 'ملفات العمولة' }}</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('/dashboard')}}">{{trans('lang.dashboard') ?: 'لوحة التحكم' }}</a></li>
                <li class="breadcrumb-item"><a href="{{ route('unified-pricing.index')}}">{{trans('unified_pricing.breadcrumb_unified_pricing') ?: 'التسعير الموحد' }}</a></li>
                <li class="breadcrumb-item active">{{trans('unified_pricing.profiles.breadcrumb') ?: 'ملفات العمولة' }}</li>
            </ol>
        </div>
        <div></div>
    </div>

    <div class="container-fluid">
        <!-- Admin Top Section - Exact Match to Zone Page -->
        <div class="admin-top-section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex top-title-section pb-4 justify-content-between">
                        <div class="d-flex top-title-left align-self-center">
                            <span class="icon mr-3"><i class="fa fa-tags fa-2x"></i></span>
                            <h3 class="mb-0">{{trans('unified_pricing.profiles.title') ?: 'إدارة ملفات العمولة' }}</h3>
                            <span class="counter ml-3 profiles_count">5</span>
                        </div>
                        <div class="d-flex top-title-right align-self-center">
                            <div class="select-box pl-3">
                                <button type="button" class="btn btn-outline-primary btn-sm mr-2" onclick="refreshProfiles()" title="{{trans('unified_pricing.refresh') ?: 'تحديث' }}">
                                    <i class="fa fa-refresh"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm mr-2" onclick="exportProfiles()" title="{{trans('unified_pricing.export') ?: 'تصدير' }}">
                                    <i class="fa fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards Row -->
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fa fa-check-circle text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">5</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.active_profiles') ?: 'الملفات النشطة' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-info">
                                    <i class="fa fa-store text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">0</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.total_restaurants') ?: 'إجمالي المطاعم' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fa fa-percentage text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">10.5%</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.avg_commission') ?: 'متوسط العمولة' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fa fa-magic text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">4</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.auto_assignable') ?: 'قابلة للتعيين التلقائي' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profiles Table -->
        <div class="table-list">
            <div class="row">
                <div class="col-12">
                    <div class="card border">
                        <div class="card-header d-flex justify-content-between align-items-center border-0">
                            <div class="card-header-title">
                                <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.profiles.table_title') ?: 'ملفات العمولة' }}</h3>
                                <p class="mb-0 text-dark-2">{{trans('unified_pricing.profiles.table_subtitle') ?: 'إدارة وتحرير ملفات العمولة المختلفة' }}</p>
                            </div>
                            <div class="card-header-right d-flex align-items-center">
                                <div class="card-header-btn mr-3">
                                    <a class="btn-primary btn rounded-full" href="{{ route('unified-pricing.profiles.create')}}">
                                        <i class="mdi mdi-plus mr-2"></i>{{trans('unified_pricing.profiles.create_new') ?: 'إنشاء ملف جديد' }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive m-t-10">
                                <table class="table table-hover table-striped table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>{{trans('unified_pricing.table.profile_name') ?: 'اسم الملف' }}</th>
                                            <th class="d-none d-md-table-cell">{{trans('unified_pricing.table.type') ?: 'النوع' }}</th>
                                            <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.business_model') ?: 'نموذج العمل' }}</th>
                                            <th class="d-none d-xl-table-cell">{{trans('unified_pricing.table.admin_commission') ?: 'عمولة الإدارة' }}</th>
                                            <th class="d-none d-xl-table-cell">{{trans('unified_pricing.table.driver_commission') ?: 'عمولة السائق' }}</th>
                                            <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.restaurants') ?: 'المطاعم' }}</th>
                                            <th>{{trans('unified_pricing.table.status') ?: 'الحالة' }}</th>
                                            <th>{{trans('unified_pricing.table.actions') ?: 'الإجراءات' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($profiles as $profile)
                                        <tr>
                                            <td>
                                                <strong>{{ $profile->display_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $profile->description ?: 'No description available' }}</small>
                                            </td>
                                            <td class="d-none d-md-table-cell">
                                                <span class="badge badge-{{ $profile->profile_type === 'starter' ? 'primary' : ($profile->profile_type === 'standard' ? 'secondary' : ($profile->profile_type === 'premium' ? 'info' : ($profile->profile_type === 'enterprise' ? 'success' : 'warning')))}}">
                                                    {{ ucfirst($profile->profile_type)}}
                                                </span>
                                            </td>
                                            <td class="d-none d-lg-table-cell">{{ ucfirst($profile->business_model)}}</td>
                                            <td class="d-none d-xl-table-cell">{{ $profile->admin_commission_rate }}%</td>
                                            <td class="d-none d-xl-table-cell">{{ $profile->driver_commission_rate }}%</td>
                                            <td class="d-none d-lg-table-cell">{{ $profile->active_restaurants_count ?? 0 }}</td>
                                            <td>
                                                <span class="badge badge-{{ $profile->is_active ? 'success' : 'danger' }}">
                                                    {{ $profile->is_active ? (__('lang.active') ?: 'نشط') : (__('lang.inactive') ?: 'غير نشط')}}
                                                </span>
                                                @if($profile->is_default)
                                                    <span class="badge badge-warning">{{trans('unified_pricing.default') ?: 'افتراضي' }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-info" title="{{trans('lang.view') ?: 'عرض' }}">
                                                        <i class="fa fa-eye"></i>
                                                    </button>
                                                    <a href="{{ route('unified-pricing.profiles.edit', $profile->id)}}" class="btn btn-sm btn-outline-primary" title="{{trans('lang.edit') ?: 'تعديل' }}">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" title="{{trans('lang.delete') ?: 'حذف' }}" onclick="confirmDelete('{{ $profile->id }}', '{{ $profile->display_name }}')">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-4">
                                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                                <p>{{trans('unified_pricing.profiles.no_profiles')}}</p>
                                                <a href="{{ route('unified-pricing.profiles.create')}}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> {{trans('unified_pricing.profiles.create_first_profile')}}
                                                </a>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Unified Pricing Profiles Styles */
.admin-top-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.top-title-section {
    align-items: center;
}

.top-title-left h3 {
    font-weight: 600;
    margin: 0;
    color: white;
}

.counter {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group .btn {
    margin: 0 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-top-section {
        padding: 1.5rem 0;
    }

    .top-title-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .top-title-right {
        width: 100%;
        justify-content: center;
    }

    .card-header-title h3 {
        font-size: 1.2rem;
    }

    .icon-circle {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin: 1px 0;
        border-radius: 4px !important;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0 15px 25px 15px;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>
@endsection

@section('scripts')
<script>
// Refresh profiles function
function refreshProfiles() {
    location.reload();
}

// Export profiles function
function exportProfiles() {
    // TODO: Implement export functionality
    alert('{{trans("unified_pricing.export_coming_soon") ?: "وظيفة التصدير قريباً" }}');
}

// Confirm delete function
function confirmDelete(profileId, profileName) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '{{trans("unified_pricing.confirm_delete") ?: "تأكيد الحذف" }}',
            text: '{{trans("unified_pricing.delete_profile_warning") ?: "هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء." }}',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '{{trans("lang.delete") ?: "حذف" }}',
            cancelButtonText: '{{trans("lang.cancel") ?: "إلغاء" }}'
        }).then((result) => {
            if (result.isConfirmed) {
                deleteProfile(profileId);
            }
        });
    } else {
        if (confirm('{{trans("unified_pricing.delete_profile_warning") ?: "هل أنت متأكد من حذف هذا الملف؟" }}')) {
            deleteProfile(profileId);
        }
    }
}

// Delete profile function
function deleteProfile(profileId) {
    fetch(`{{ route('unified-pricing.profiles.destroy', '')}}/${profileId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token()}}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof Swal !== 'undefined') {
                Swal.fire('{{ (__("unified_pricing.deleted") ?: "تم الحذف!")}}', '{{ (__("unified_pricing.profile_deleted_successfully") ?: "تم حذف الملف بنجاح")}}', 'success');
            } else {
                alert('{{ (__("unified_pricing.profile_deleted_successfully") ?: "تم حذف الملف بنجاح")}}');
            }
            location.reload();
        } else {
            if (typeof Swal !== 'undefined') {
                Swal.fire('{{trans("lang.error") ?: "خطأ" }}', data.message, 'error');
            } else {
                alert('{{trans("lang.error") ?: "خطأ" }}: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire('{{ (__("lang.error") ?: "خطأ")}}', '{{ (__("unified_pricing.delete_failed") ?: "فشل في حذف الملف")}}', 'error');
        } else {
            alert('{{ (__("unified_pricing.delete_failed") ?: "فشل في حذف الملف")}}');
        }
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Update counter with animation
    const counter = document.querySelector('.profiles_count');
    if (counter) {
        const target = parseInt(counter.textContent);
        let current = 0;
        const increment = target / 20;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 50);
    }
});
</script>
@endsection
