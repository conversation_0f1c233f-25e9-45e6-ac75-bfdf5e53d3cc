@extends('layouts.app')

@section('content')
<div class="page-wrapper">
    <!-- Page Titles - Exact Match to Zone Page -->
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">{{trans('unified_pricing.page_title')}}</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('/dashboard')}}">{{trans('lang.dashboard')}}</a></li>
                <li class="breadcrumb-item active">{{trans('unified_pricing.breadcrumb_unified_pricing')}}</li>
            </ol>
        </div>
        <div></div>
    </div>

    <div class="container-fluid">
        <!-- Admin Top Section - Exact Match to Zone Page -->
        <div class="admin-top-section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex top-title-section pb-4 justify-content-between">
                        <div class="d-flex top-title-left align-self-center">
                            <span class="icon mr-3"><i class="fa fa-calculator fa-2x"></i></span>
                            <h3 class="mb-0">{{trans('unified_pricing.dashboard.title')}}</h3>
                            <span class="counter ml-3 pricing_count">{{ $stats['total_profiles'] ?? 0 }}</span>
                        </div>
                        <div class="d-flex top-title-right align-self-center">
                            <div class="select-box pl-3">
                                <button type="button" class="btn btn-outline-primary btn-sm mr-2" onclick="refreshDashboard()" title="{{trans('unified_pricing.dashboard.refresh')}}">
                                    <i class="fa fa-refresh"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm mr-2" onclick="autoAssignProfiles()" title="{{trans('unified_pricing.dashboard.auto_assign')}}">
                                    <i class="fa fa-magic"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards Row -->
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fa fa-store text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">{{ number_format($stats['total_restaurants'] ?? 0) }}</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.active_restaurants')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fa fa-tags text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">{{ number_format($stats['total_profiles'] ?? 0) }}</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.commission_profiles')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-info">
                                    <i class="fa fa-dollar-sign text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">${{ number_format($stats['total_commission_earned'] ?? 0, 2) }}</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.total_commission')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fa fa-arrow-up text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">{{ number_format($stats['upgrade_candidates'] ?? 0) }}</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.upgrade_candidates')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tables -->
        <div class="table-list">
            <div class="row">
                <!-- Commission Profiles Overview -->
                <div class="col-lg-6 col-md-12">
                    <div class="card border">
                        <div class="card-header d-flex justify-content-between align-items-center border-0">
                            <div class="card-header-title">
                                <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.profiles.title')}}</h3>
                                <p class="mb-0 text-dark-2">{{trans('unified_pricing.profiles.subtitle')}}</p>
                            </div>
                            <div class="card-header-right d-flex align-items-center">
                                <div class="card-header-btn mr-3">
                                    <a class="btn-primary btn rounded-full" href="{{ route('unified-pricing.profiles') }}">
                                        <i class="mdi mdi-eye mr-2"></i>{{trans('unified_pricing.view_all')}}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(isset($profiles) && $profiles->count() > 0)
                                <div class="table-responsive m-t-10">
                                    <table class="table table-hover table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>{{trans('unified_pricing.table.profile')}}</th>
                                                <th class="d-none d-md-table-cell">{{trans('unified_pricing.table.type')}}</th>
                                                <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.restaurants')}}</th>
                                                <th>{{trans('unified_pricing.table.status')}}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($profiles->take(5) as $profile)
                                            <tr>
                                                <td>
                                                    <strong>{{ $profile->display_name ?? trans('unified_pricing.standard_profile') }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $profile->business_model ?? trans('unified_pricing.percentage_model') }}</small>
                                                </td>
                                                <td class="d-none d-md-table-cell">
                                                    <span class="badge badge-{{ (($profile->profile_type ?? 'standard') === 'enterprise') ? 'success' : ((($profile->profile_type ?? 'standard') === 'premium') ? 'info' : 'secondary') }}">
                                                        {{ trans('unified_pricing.profile_types.' . ($profile->profile_type ?? 'standard')) }}
                                                    </span>
                                                </td>
                                                <td class="d-none d-lg-table-cell">{{ $profile->active_restaurants_count ?? 0 }}</td>
                                                <td>
                                                    <span class="badge badge-{{ ($profile->is_active ?? true) ? 'success' : 'danger' }}">
                                                        {{ ($profile->is_active ?? true) ? trans('unified_pricing.active_status') : trans('unified_pricing.inactive_status') }}
                                                    </span>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fa fa-tags fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">{{trans('unified_pricing.no_profiles')}}</h4>
                                    <p class="text-muted">{{trans('unified_pricing.no_profiles_desc')}}</p>
                                    <a href="{{ route('unified-pricing.profiles.create') }}" class="btn btn-primary">
                                        <i class="mdi mdi-plus mr-2"></i>{{trans('unified_pricing.create_first_profile')}}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Recent Integrations -->
                <div class="col-lg-6 col-md-12">
                    <div class="card border">
                        <div class="card-header d-flex justify-content-between align-items-center border-0">
                            <div class="card-header-title">
                                <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.integrations.title')}}</h3>
                                <p class="mb-0 text-dark-2">{{trans('unified_pricing.integrations.subtitle')}}</p>
                            </div>
                            <div class="card-header-right d-flex align-items-center">
                                <div class="card-header-btn mr-3">
                                    <a class="btn-primary btn rounded-full" href="{{ route('unified-pricing.restaurants') }}">
                                        <i class="mdi mdi-eye mr-2"></i>{{trans('unified_pricing.view_all')}}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(isset($recentIntegrations) && $recentIntegrations->count() > 0)
                                <div class="table-responsive m-t-10">
                                    <table class="table table-hover table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>{{trans('unified_pricing.table.restaurant')}}</th>
                                                <th class="d-none d-md-table-cell">{{trans('unified_pricing.table.profile')}}</th>
                                                <th>{{trans('unified_pricing.table.status')}}</th>
                                                <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.date')}}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentIntegrations as $integration)
                                            <tr>
                                                <td>
                                                    <strong>{{ $integration->restaurant->name ?? 'مطعم تجريبي' }}</strong>
                                                </td>
                                                <td class="d-none d-md-table-cell">
                                                    <small>{{ $integration->commissionProfile->display_name ?? trans('unified_pricing.standard_profile') }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge badge-{{ ($integration->integration_status ?? 'active') === 'active' ? 'success' : 'warning' }}">
                                                        {{ trans('unified_pricing.status.' . ($integration->integration_status ?? 'active')) }}
                                                    </span>
                                                </td>
                                                <td class="d-none d-lg-table-cell">
                                                    <small>{{ isset($integration->created_at) ? $integration->created_at->format('M d, Y') : date('M d, Y') }}</small>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fa fa-link fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">{{trans('unified_pricing.no_integrations')}}</h4>
                                    <p class="text-muted">{{trans('unified_pricing.no_integrations_desc')}}</p>
                                    <button type="button" class="btn btn-success" onclick="autoAssignProfiles()">
                                        <i class="mdi mdi-magic mr-2"></i>{{trans('unified_pricing.auto_assign_profiles')}}
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card border">
                    <div class="card-header d-flex justify-content-between align-items-center border-0">
                        <div class="card-header-title">
                            <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.quick_actions.title')}}</h3>
                            <p class="mb-0 text-dark-2">{{trans('unified_pricing.quick_actions.subtitle')}}</p>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                <a href="{{ route('unified-pricing.profiles') }}" class="btn btn-outline-primary btn-block py-3">
                                    <i class="fa fa-tags fa-2x d-block mb-2"></i>
                                    <span>{{trans('unified_pricing.actions.manage_profiles')}}</span>
                                </a>
                            </div>
                            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                <a href="{{ route('unified-pricing.restaurants') }}" class="btn btn-outline-info btn-block py-3">
                                    <i class="fa fa-store fa-2x d-block mb-2"></i>
                                    <span>{{trans('unified_pricing.actions.restaurant_integration')}}</span>
                                </a>
                            </div>
                            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                <button type="button" class="btn btn-outline-success btn-block py-3" onclick="evaluateUpgrades()">
                                    <i class="fa fa-arrow-up fa-2x d-block mb-2"></i>
                                    <span>{{trans('unified_pricing.actions.evaluate_upgrades')}}</span>
                                </button>
                            </div>
                            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                                <a href="{{ route('unified-pricing.analytics') }}" class="btn btn-outline-warning btn-block py-3">
                                    <i class="fa fa-chart-bar fa-2x d-block mb-2"></i>
                                    <span>{{trans('unified_pricing.actions.analytics')}}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">{{trans('unified_pricing.loading')}}</span>
                </div>
                <p class="mt-2 mb-0">{{trans('unified_pricing.processing')}}</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Unified Pricing Dashboard Styles */
.admin-top-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.top-title-section {
    align-items: center;
}

.top-title-left h3 {
    font-weight: 600;
    margin: 0;
    color: white;
}

.counter {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-outline-primary:hover,
.btn-outline-info:hover,
.btn-outline-success:hover,
.btn-outline-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-top-section {
        padding: 1.5rem 0;
    }

    .top-title-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .top-title-right {
        width: 100%;
        justify-content: center;
    }

    .card-header-title h3 {
        font-size: 1.2rem;
    }

    .icon-circle {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0 15px 25px 15px;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-block {
        padding: 1rem;
        font-size: 0.9rem;
    }
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
@endsection

@section('scripts')
<script>
// Loading overlay functions
function showLoading() {
    if (!document.getElementById('loadingOverlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="text-center text-white">
                <div class="loading-spinner"></div>
                <p class="mt-3">{{trans('unified_pricing.loading')}}</p>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// Auto-assign profiles function
function autoAssignProfiles() {
    showLoading();

    fetch('{{ route("unified-pricing.auto-assign-profiles") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            const successful = data.results.successful.length || 0;
            const failed = data.results.failed.length || 0;

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '{{trans("unified_pricing.auto_assign_complete")}}',
                    html: `
                        <p><strong>{{trans("unified_pricing.successfully_assigned")}}</strong> ${successful} {{trans("unified_pricing.restaurants")}}</p>
                        <p><strong>{{trans("unified_pricing.failed")}}</strong> ${failed} {{trans("unified_pricing.restaurants")}}</p>
                    `,
                    icon: 'success',
                    confirmButtonText: '{{trans("lang.ok")}}'
                }).then(() => {
                    location.reload();
                });
            } else {
                alert('{{trans("unified_pricing.auto_assign_success")}}');
                location.reload();
            }
        } else {
            if (typeof Swal !== 'undefined') {
                Swal.fire('{{trans("lang.error")}}', data.message, 'error');
            } else {
                alert('{{trans("lang.error")}}: ' + data.message);
            }
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Auto-assign error:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire('{{trans("lang.error")}}', '{{trans("unified_pricing.auto_assign_failed")}}', 'error');
        } else {
            alert('{{trans("unified_pricing.auto_assign_failed")}}');
        }
    });
}

// Evaluate upgrades function
function evaluateUpgrades() {
    showLoading();

    fetch('{{ route("unified-pricing.evaluate-upgrades") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            const evaluated = data.results.evaluated || 0;
            const upgraded = data.results.upgraded || 0;
            const noChange = data.results.no_change || 0;

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '{{trans("unified_pricing.upgrade_evaluation_complete")}}',
                    html: `
                        <p><strong>{{trans("unified_pricing.evaluated")}}</strong> ${evaluated} {{trans("unified_pricing.restaurants")}}</p>
                        <p><strong>{{trans("unified_pricing.upgraded")}}</strong> ${upgraded} {{trans("unified_pricing.restaurants")}}</p>
                        <p><strong>{{trans("unified_pricing.no_change")}}</strong> ${noChange} {{trans("unified_pricing.restaurants")}}</p>
                    `,
                    icon: 'success',
                    confirmButtonText: '{{trans("lang.ok")}}'
                }).then(() => {
                    location.reload();
                });
            } else {
                alert('{{trans("unified_pricing.upgrade_evaluation_success")}}');
                location.reload();
            }
        } else {
            if (typeof Swal !== 'undefined') {
                Swal.fire('{{trans("lang.error")}}', data.message, 'error');
            } else {
                alert('{{trans("lang.error")}}: ' + data.message);
            }
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Upgrade evaluation error:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire('{{trans("lang.error")}}', '{{trans("unified_pricing.upgrade_evaluation_failed")}}', 'error');
        } else {
            alert('{{trans("unified_pricing.upgrade_evaluation_failed")}}');
        }
    });
}

// Refresh dashboard function
function refreshDashboard() {
    showLoading();
    setTimeout(() => {
        location.reload();
    }, 500);
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Update counters with animation
    const counters = document.querySelectorAll('.pricing_count');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        let current = 0;
        const increment = target / 20;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 50);
    });
});
</script>
@endsection

