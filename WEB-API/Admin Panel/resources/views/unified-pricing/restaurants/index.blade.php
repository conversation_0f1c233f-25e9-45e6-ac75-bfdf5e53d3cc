@extends('layouts.app')

@section('content')
<div class="page-wrapper">
    <!-- Page Titles - Exact Match to Zone Page -->
    <div class="row page-titles">
        <div class="col-md-5 align-self-center">
            <h3 class="text-themecolor">{{trans('unified_pricing.restaurants.page_title') ?: 'تكامل تسعير المطاعم' }}</h3>
        </div>
        <div class="col-md-7 align-self-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('/dashboard')}}">{{trans('lang.dashboard') ?: 'لوحة التحكم' }}</a></li>
                <li class="breadcrumb-item"><a href="{{ route('unified-pricing.index')}}">{{trans('unified_pricing.breadcrumb_unified_pricing') ?: 'التسعير الموحد' }}</a></li>
                <li class="breadcrumb-item active">{{trans('unified_pricing.restaurants.breadcrumb') ?: 'تكامل المطاعم' }}</li>
            </ol>
        </div>
        <div></div>
    </div>

    <div class="container-fluid">
        <!-- Admin Top Section - Exact Match to Zone Page -->
        <div class="admin-top-section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex top-title-section pb-4 justify-content-between">
                        <div class="d-flex top-title-left align-self-center">
                            <span class="icon mr-3"><i class="fa fa-store fa-2x"></i></span>
                            <h3 class="mb-0">{{trans('unified_pricing.restaurants.title') ?: 'إدارة تكامل المطاعم' }}</h3>
                            <span class="counter ml-3 restaurants_count">0</span>
                        </div>
                        <div class="d-flex top-title-right align-self-center">
                            <div class="select-box pl-3">
                                <button type="button" class="btn btn-outline-primary btn-sm mr-2" onclick="refreshRestaurants()" title="{{trans('unified_pricing.refresh') ?: 'تحديث' }}">
                                    <i class="fa fa-refresh"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm mr-2" onclick="autoAssignAll()" title="{{trans('unified_pricing.auto_assign_all') ?: 'تعيين تلقائي للكل' }}">
                                    <i class="fa fa-magic"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards Row -->
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fa fa-store text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">0</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.total_restaurants') ?: 'إجمالي المطاعم' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fa fa-check-circle text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">0</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.active_integrations') ?: 'التكاملات النشطة' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fa fa-clock text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">0</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.pending_integration') ?: 'في انتظار التكامل' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                <div class="card border">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-info">
                                    <i class="fa fa-percentage text-white"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-0 font-weight-bold">0%</h4>
                                <p class="text-muted mb-0">{{trans('unified_pricing.stats.integration_rate') ?: 'معدل التكامل' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row">
            <div class="col-12">
                <div class="card border">
                    <div class="card-header d-flex justify-content-between align-items-center border-0">
                        <div class="card-header-title">
                            <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.filters.title') ?: 'البحث والفلترة' }}</h3>
                            <p class="mb-0 text-dark-2">{{trans('unified_pricing.filters.subtitle') ?: 'البحث عن المطاعم وفلترة النتائج' }}</p>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                                <label for="statusFilter">{{trans('unified_pricing.filters.status') ?: 'حالة التكامل' }}</label>
                                <select class="form-control" id="statusFilter">
                                    <option value="">{{trans('unified_pricing.filters.all_statuses') ?: 'جميع الحالات' }}</option>
                                    <option value="active">{{trans('lang.active') ?: 'نشط' }}</option>
                                    <option value="pending">{{trans('unified_pricing.status.pending') ?: 'في الانتظار' }}</option>
                                    <option value="suspended">{{trans('unified_pricing.status.suspended') ?: 'معلق' }}</option>
                                    <option value="inactive">{{trans('lang.inactive') ?: 'غير نشط' }}</option>
                                </select>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                                <label for="profileFilter">{{trans('unified_pricing.filters.profile') ?: 'ملف العمولة' }}</label>
                                <select class="form-control" id="profileFilter">
                                    <option value="">{{trans('unified_pricing.filters.all_profiles') ?: 'جميع الملفات' }}</option>
                                    <option value="starter">{{trans('unified_pricing.types.starter') ?: 'مبتدئ' }}</option>
                                    <option value="standard">{{trans('unified_pricing.types.standard') ?: 'عادي' }}</option>
                                    <option value="premium">{{trans('unified_pricing.types.premium') ?: 'مميز' }}</option>
                                    <option value="enterprise">{{trans('unified_pricing.types.enterprise') ?: 'مؤسسي' }}</option>
                                </select>
                            </div>
                            <div class="col-lg-4 col-md-8 col-sm-8 col-12 mb-3">
                                <label for="searchInput">{{trans('unified_pricing.filters.search_restaurant') ?: 'البحث عن مطعم' }}</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="{{trans('unified_pricing.filters.search_placeholder') ?: 'البحث باسم المطعم...' }}">
                            </div>
                            <div class="col-lg-2 col-md-4 col-sm-4 col-12 mb-3">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-primary btn-block" onclick="searchRestaurants()">
                                    <i class="fa fa-search"></i> {{trans('lang.search') ?: 'بحث' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Restaurant Integrations Table -->
        <div class="table-list">
            <div class="row">
                <div class="col-12">
                    <div class="card border">
                        <div class="card-header d-flex justify-content-between align-items-center border-0">
                            <div class="card-header-title">
                                <h3 class="text-dark-2 mb-2 h4">{{trans('unified_pricing.restaurants.table_title') ?: 'تكاملات تسعير المطاعم' }}</h3>
                                <p class="mb-0 text-dark-2">{{trans('unified_pricing.restaurants.table_subtitle') ?: 'إدارة ربط المطاعم بنظام التسعير الموحد' }}</p>
                            </div>
                            <div class="card-header-right d-flex align-items-center">
                                <div class="card-header-btn mr-3">
                                    <button class="btn-success btn rounded-full" onclick="autoAssignAll()">
                                        <i class="mdi mdi-magic mr-2"></i>{{trans('unified_pricing.auto_assign_all') ?: 'تعيين تلقائي للكل' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive m-t-10">
                                <table class="table table-hover table-striped table-bordered" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>{{trans('unified_pricing.table.restaurant') ?: 'المطعم' }}</th>
                                            <th class="d-none d-md-table-cell">{{trans('unified_pricing.table.commission_profile') ?: 'ملف العمولة' }}</th>
                                            <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.commission_rates') ?: 'معدلات العمولة' }}</th>
                                            <th class="d-none d-xl-table-cell">{{trans('unified_pricing.table.orders_processed') ?: 'الطلبات المعالجة' }}</th>
                                            <th class="d-none d-xl-table-cell">{{trans('unified_pricing.table.total_commission') ?: 'إجمالي العمولة' }}</th>
                                            <th>{{trans('unified_pricing.table.status') ?: 'الحالة' }}</th>
                                            <th class="d-none d-lg-table-cell">{{trans('unified_pricing.table.last_updated') ?: 'آخر تحديث' }}</th>
                                            <th>{{trans('unified_pricing.table.actions') ?: 'الإجراءات' }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="8" class="text-center py-5">
                                                <div class="text-muted">
                                                    <i class="fa fa-info-circle fa-3x mb-3"></i>
                                                    <h4>{{trans('unified_pricing.no_restaurant_integrations') ?: 'لا توجد تكاملات مطاعم' }}</h4>
                                                    <p>{{trans('unified_pricing.no_restaurant_integrations_desc') ?: 'لم يتم ربط أي مطاعم بنظام التسعير بعد.' }}</p>
                                                    <button type="button" class="btn btn-success" onclick="autoAssignAll()">
                                                        <i class="fa fa-magic"></i> {{trans('unified_pricing.auto_assign_profiles_to_all') ?: 'تعيين ملفات تلقائياً لجميع المطاعم' }}
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Unified Pricing Restaurants Styles */
.admin-top-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.top-title-section {
    align-items: center;
}

.top-title-left h3 {
    font-weight: 600;
    margin: 0;
    color: white;
}

.counter {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-top-section {
        padding: 1.5rem 0;
    }

    .top-title-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .top-title-right {
        width: 100%;
        justify-content: center;
    }

    .card-header-title h3 {
        font-size: 1.2rem;
    }

    .icon-circle {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0 15px 25px 15px;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>
@endsection

@section('scripts')
<script>
// Refresh restaurants function
function refreshRestaurants() {
    location.reload();
}

// Auto-assign all function
function autoAssignAll() {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '{{trans("unified_pricing.confirm_auto_assign") ?: "تأكيد التعيين التلقائي" }}',
            text: '{{trans("unified_pricing.auto_assign_warning") ?: "سيتم تعيين ملفات العمولة المناسبة لجميع المطاعم تلقائياً. هل تريد المتابعة؟" }}',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '{{trans("unified_pricing.yes_assign") ?: "نعم، قم بالتعيين" }}',
            cancelButtonText: '{{trans("lang.cancel") ?: "إلغاء" }}'
        }).then((result) => {
            if (result.isConfirmed) {
                performAutoAssign();
            }
        });
    } else {
        if (confirm('{{trans("unified_pricing.auto_assign_warning") ?: "سيتم تعيين ملفات العمولة المناسبة لجميع المطاعم تلقائياً. هل تريد المتابعة؟" }}')) {
            performAutoAssign();
        }
    }
}

// Perform auto-assign
function performAutoAssign() {
    // Show loading
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: '{{trans("unified_pricing.processing") ?: "جاري المعالجة..." }}',
            text: '{{trans("unified_pricing.please_wait") ?: "يرجى الانتظار..." }}',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    // Simulate API call (replace with actual endpoint)
    setTimeout(() => {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '{{ (__("unified_pricing.auto_assign_complete") ?: "تم التعيين التلقائي!")}}',
                html: `
                    <p><strong>{{ (__("unified_pricing.successfully_assigned") ?: "تم تعيين بنجاح:")}}</strong> 0 {{ (__("unified_pricing.restaurants_count") ?: "مطعم")}}</p>
                    <p><strong>{{ (__("unified_pricing.failed") ?: "فشل:")}}</strong> 0 {{ (__("unified_pricing.restaurants_count") ?: "مطعم")}}</p>
                `,
                icon: 'info',
                confirmButtonText: '{{ (__("lang.ok") ?: "موافق")}}'
            });
        } else {
            alert('{{trans("unified_pricing.no_restaurants_to_assign") ?: "لا توجد مطاعم للتعيين" }}');
        }
    }, 2000);
}

// Search restaurants function
function searchRestaurants() {
    const status = document.getElementById('statusFilter').value;
    const profile = document.getElementById('profileFilter').value;
    const search = document.getElementById('searchInput').value;

    // TODO: Implement actual search functionality
    console.log('Searching with:', { status, profile, search });

    // For now, just show a message
    if (typeof Swal !== 'undefined') {
        Swal.fire('{{ (__("unified_pricing.search_coming_soon") ?: "البحث قريباً")}}', '{{ (__("unified_pricing.search_functionality_coming_soon") ?: "وظيفة البحث ستكون متاحة قريباً")}}', 'info');
    } else {
        alert('{{ (__("unified_pricing.search_coming_soon") ?: "البحث قريباً")}}');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Update counter with animation
    const counter = document.querySelector('.restaurants_count');
    if (counter) {
        const target = parseInt(counter.textContent);
        let current = 0;
        const increment = target / 20;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 50);
    }

    // Add search on enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRestaurants();
        }
    });
});
</script>
@endsection
